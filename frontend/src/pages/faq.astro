---
import Layout from '@/layouts/Layout.astro'
import { FAQPage } from '@/components/faq-page'
import { getFAQs, type FAQ } from '@/lib/api'

// Получаем данные из PocketBase
let faqs: FAQ[] = [];

try {
  const response = await getFAQs();
  console.log('Получено FAQ из PocketBase на странице FAQ:', response?.length || 0);

  if (response && response.length > 0) {
    faqs = response;
  } else {
    console.warn('Нет данных о FAQ в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении FAQ:', error);
}
---

<Layout title="Часто задаваемые вопросы">
  <FAQPage faqs={faqs} client:load />
</Layout>
