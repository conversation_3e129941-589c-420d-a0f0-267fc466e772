---
import Layout from '../layouts/Layout.astro';
import { PricesPage } from '../components/prices-page';
import PocketBase from 'pocketbase';

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

// Получаем данные из PocketBase
const pb = new PocketBase(PUBLIC_API_URL);
let priceCategories = [];
let prices = [];

try {
  priceCategories = await pb.collection('service_categories').getFullList({
    sort: 'name',
  });

  prices = await pb.collection('prices').getFullList({
    sort: 'sort_order',
    expand: 'category,service',
  });

  console.log('Получено категорий цен:', priceCategories.length);
  console.log('Получено цен:', prices.length);
} catch (error) {
  console.error('Ошибка при получении данных:', error);
}
---

<Layout title="Цены на услуги | Стоматология Стомлайн в Мурманске"
  description="Актуальный прайс-лист на все стоматологические услуги в клинике Стомлайн. Доступные цены, скидки и акции.">

  <PricesPage prices={prices} categories={priceCategories} client:load />
</Layout>
