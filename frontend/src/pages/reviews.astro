---
import Layout from '@/layouts/Layout.astro'
import { ReviewsPage } from '@/components/reviews-page'
import { getReviews, type Review } from '@/lib/api'

// Получаем данные из PocketBase
let reviews: Review[] = [];

try {
  const response = await getReviews();
  console.log('Получено отзывов из PocketBase на странице отзывов:', response?.length || 0);
  
  if (response && response.length > 0) {
    reviews = response;
  } else {
    console.warn('Нет данных об отзывах в PocketBase');
  }
} catch (error) {
  console.error('Ошибка при получении отзывов:', error);
}
---

<Layout title="Отзывы наших пациентов">
  <ReviewsPage reviews={reviews} client:load />
</Layout>
