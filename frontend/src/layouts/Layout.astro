---
import Header from '@/components/header.tsx'
import Footer from '@/components/footer.tsx'
import { AuthSync } from '@/components/admin/AuthSync'
import { AccessibilityProvider } from '@/components/accessibility/AccessibilityProvider'
import '../styles/global.css'
const { title, description } = Astro.props

const defaultTitle = 'STOM-LINE - Стоматологическая клиника'
const defaultDescription = 'Стоматологическая клиника STOM-LINE - современное оборудование, профессиональные врачи и комфортное лечение.'
---

<html lang='ru'>
  <head>
    <meta charset='utf-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1' />
    <link rel='icon' type='image/svg+xml' href='/favicon.svg' />
    <title>{title || defaultTitle}</title>
    <meta name='description' content={description || defaultDescription} />
    <meta name='theme-color' content='#8BC34A' />

    <!-- Мета-теги для доступности -->
    <meta name='accessibility-features' content='high-contrast, large-text, keyboard-navigation, screen-reader-support' />
    <meta name='accessibility-compliance' content='GOST R 52872-2019, WCAG 2.1 AA' />
    <meta name='accessibility-help' content='Для включения версии для слабовидящих нажмите кнопку "Для слабовидящих" в верхней части страницы' />
    <meta property='og:title' content={title || defaultTitle} />
    <meta property='og:description' content={description || defaultDescription} />
    <meta property='og:type' content='website' />
    <meta property='og:url' content='https://stom-line.ru' />
    <meta property='og:image' content='/og-image.jpg' />
    <link rel='preconnect' href='https://fonts.googleapis.com' />
    <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin />
    <link href='https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap' rel='stylesheet' />
  </head>
  <body class='font-montserrat'>
    <!-- Скрытая ссылка для перехода к основному контенту -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:bg-black focus:text-white focus:px-4 focus:py-2 focus:rounded">
      Перейти к основному содержанию
    </a>

    <AccessibilityProvider client:load>
      <AuthSync client:load />
      <Header client:load/>
      <main id="main-content" role="main" aria-label="Основное содержание страницы" class="relative flex min-h-screen flex-col bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
        <slot />
      </main>
      <Footer client:only="react"/>
    </AccessibilityProvider>

    <!-- Скрытый элемент для объявлений скринридера -->
    <div id="accessibility-announcements" aria-live="polite" aria-atomic="true" class="sr-only"></div>
  </body>
</html>
