import { useState } from 'react';
import { motion } from 'framer-motion';
import { SearchIcon, FilterIcon, DownloadIcon, InfoIcon, CheckCircleIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { EditButton } from '@/components/admin/EditButton';

interface Price {
  id: string;
  name: string;
  price: number;
  price_prefix?: string;
  price_suffix?: string;
  unit?: string;
  is_active?: boolean;
  sort_order?: number;
  category?: string;
  service?: string;
  expand?: {
    category?: {
      id: string;
      name: string;
    };
    service?: {
      id: string;
      name: string;
    };
  };
}

interface PricesPageProps {
  prices: Price[];
  categories: any[];
}

export const PricesPage = ({ prices, categories }: PricesPageProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Фильтрация цен
  const filteredPrices = prices.filter(price => {
    const matchesSearch = price.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || 
                           price.expand?.category?.id === selectedCategory;
    
    return matchesSearch && matchesCategory && price.is_active !== false;
  });

  // Группировка цен по категориям
  const pricesByCategory = categories.map(category => {
    const categoryPrices = filteredPrices.filter(price => 
      price.expand?.category?.id === category.id
    );
    return {
      ...category,
      prices: categoryPrices
    };
  }).filter(category => category.prices.length > 0);

  const formatPrice = (price: Price) => {
    let formattedPrice = '';
    
    if (price.price_prefix) {
      formattedPrice += price.price_prefix + ' ';
    }
    
    formattedPrice += price.price.toLocaleString('ru-RU');
    
    if (price.price_suffix) {
      formattedPrice += ' ' + price.price_suffix;
    } else {
      formattedPrice += ' ₽';
    }
    
    if (price.unit) {
      formattedPrice += ' / ' + price.unit;
    }
    
    return formattedPrice;
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]" />

      <div className="relative z-10 container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge className="bg-olive-500 hover:bg-olive-600 text-white mb-6">
            Прайс-лист
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Цены на услуги
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Прозрачное ценообразование и доступные цены на все виды стоматологических услуг
          </p>

          {/* Search and Filters */}
          <div className="max-w-2xl mx-auto space-y-4">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Поиск услуг..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 text-lg border-olive-200 focus:border-olive-400"
              />
            </div>

            {/* Category filters */}
            <div className="flex flex-wrap gap-2 justify-center">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                onClick={() => setSelectedCategory('all')}
                className={selectedCategory === 'all' ? 
                  'bg-olive-500 hover:bg-olive-600 text-white' : 
                  'border-olive-200 text-olive-700 hover:bg-olive-50'
                }
              >
                Все категории
              </Button>
              {categories.map(category => (
                <Button
                  key={category.id}
                  variant={selectedCategory === category.id ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category.id)}
                  className={selectedCategory === category.id ? 
                    'bg-olive-500 hover:bg-olive-600 text-white' : 
                    'border-olive-200 text-olive-700 hover:bg-olive-50'
                  }
                >
                  {category.name}
                </Button>
              ))}
            </div>

            {/* Download button */}
            <div className="flex justify-center">
              <Button variant="outline" className="border-olive-200 text-olive-700 hover:bg-olive-50">
                <DownloadIcon className="mr-2 h-4 w-4" />
                Скачать прайс-лист
              </Button>
            </div>
          </div>
        </div>

        {/* Prices by Category */}
        {pricesByCategory.length > 0 ? (
          <div className="space-y-8">
            {pricesByCategory.map((category, categoryIndex) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: categoryIndex * 0.1 }}
              >
                <Card className="bg-white/80 backdrop-blur-sm border-olive-100 shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                      <div className="w-8 h-8 bg-olive-100 rounded-full flex items-center justify-center">
                        <CheckCircleIcon className="h-5 w-5 text-olive-600" />
                      </div>
                      {category.name}
                      <Badge variant="secondary" className="ml-auto">
                        {category.prices.length} услуг
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="border-b border-olive-100">
                            <th className="text-left py-3 px-4 font-semibold text-gray-700">
                              Наименование услуги
                            </th>
                            <th className="text-right py-3 px-4 font-semibold text-gray-700">
                              Стоимость
                            </th>
                            <th className="w-16"></th>
                          </tr>
                        </thead>
                        <tbody>
                          {category.prices.map((price, priceIndex) => (
                            <motion.tr
                              key={price.id}
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              transition={{ duration: 0.3, delay: priceIndex * 0.05 }}
                              className="border-b border-gray-100 hover:bg-olive-50/50 group"
                            >
                              <td className="py-4 px-4">
                                <div className="font-medium text-gray-900">
                                  {price.name}
                                </div>
                              </td>
                              <td className="py-4 px-4 text-right">
                                <div className="font-semibold text-olive-600">
                                  {formatPrice(price)}
                                </div>
                              </td>
                              <td className="py-4 px-4">
                                <EditButton
                                  collection="prices"
                                  id={price.id}
                                  position="inline"
                                  size="sm"
                                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                />
                              </td>
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-olive-100 rounded-full flex items-center justify-center">
                <SearchIcon className="h-12 w-12 text-olive-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {searchTerm ? 'Услуги не найдены' : 'Цены загружаются'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm
                  ? 'Попробуйте изменить поисковый запрос или выбрать другую категорию'
                  : 'Пожалуйста, подождите...'
                }
              </p>
              {searchTerm && (
                <Button
                  variant="outline"
                  onClick={() => setSearchTerm('')}
                  className="border-olive-200 text-olive-700 hover:bg-olive-50"
                >
                  Очистить поиск
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Important Information */}
        <div className="mt-16">
          <Card className="bg-olive-50/80 backdrop-blur-sm border-olive-200">
            <CardContent className="p-8">
              <div className="flex items-start gap-4">
                <div className="w-8 h-8 bg-olive-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <InfoIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    Важная информация
                  </h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start gap-2">
                      <CheckCircleIcon className="h-5 w-5 text-olive-500 mt-0.5 flex-shrink-0" />
                      Цены на сайте носят информационный характер и не являются публичной офертой
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircleIcon className="h-5 w-5 text-olive-500 mt-0.5 flex-shrink-0" />
                      Окончательная стоимость определяется после консультации и диагностики
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircleIcon className="h-5 w-5 text-olive-500 mt-0.5 flex-shrink-0" />
                      Для уточнения актуальных цен обращайтесь по телефону клиники
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircleIcon className="h-5 w-5 text-olive-500 mt-0.5 flex-shrink-0" />
                      В клинике действуют специальные предложения и скидки для постоянных пациентов
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="mt-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-olive-100 text-center max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Нужна консультация по ценам?
            </h3>
            <p className="text-gray-600 mb-6">
              Наши специалисты помогут подобрать оптимальный план лечения с учетом вашего бюджета
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-olive-500 hover:bg-olive-600 text-white">
                Записаться на консультацию
              </Button>
              <Button variant="outline" className="border-olive-200 text-olive-700 hover:bg-olive-50">
                Позвонить сейчас
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
