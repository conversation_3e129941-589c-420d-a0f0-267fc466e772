import { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDownIcon, SearchIcon, HelpCircleIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { EditButton } from '@/components/admin/EditButton';
import { type FAQ } from '@/lib/api';

interface FAQPageProps {
  faqs: FAQ[];
}

interface FAQItemProps {
  faq: FAQ;
  index: number;
  isOpen: boolean;
  onToggle: () => void;
}

const FAQItem = ({ faq, index, isOpen, onToggle }: FAQItemProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group relative bg-white/80 backdrop-blur-sm rounded-xl border border-olive-100 shadow-sm hover:shadow-md transition-all duration-300"
    >
      <EditButton
        collection="faq"
        id={faq.id}
        position="top-right"
        variant="text"
        size="sm"
        className="bg-white/90 border border-gray-200 shadow-sm"
      />
      
      <button
        onClick={onToggle}
        className="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-olive-500 focus:ring-offset-2 rounded-xl"
      >
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 pr-8">
            {faq.question}
          </h3>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3 }}
            className="flex-shrink-0"
          >
            <ChevronDownIcon className="h-5 w-5 text-olive-600" />
          </motion.div>
        </div>
      </button>
      
      <motion.div
        initial={false}
        animate={{
          height: isOpen ? 'auto' : 0,
          opacity: isOpen ? 1 : 0
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className="overflow-hidden"
      >
        <div className="px-6 pb-6">
          <div 
            className="prose prose-olive max-w-none text-gray-700"
            dangerouslySetInnerHTML={{ __html: faq.answer || '' }}
          />
        </div>
      </motion.div>
    </motion.div>
  );
};

export const FAQPage = ({ faqs }: FAQPageProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Получаем уникальные категории
  const categories = Array.from(new Set(faqs.map(faq => faq.category || 'Общие')));

  // Фильтрация FAQ
  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (faq.answer && faq.answer.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || 
                           (faq.category || 'Общие') === selectedCategory;
    
    return matchesSearch && matchesCategory && faq.is_published !== false;
  });

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  const expandAll = () => {
    setOpenItems(new Set(filteredFAQs.map(faq => faq.id)));
  };

  const collapseAll = () => {
    setOpenItems(new Set());
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]" />

      <div className="relative z-10 container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge className="bg-olive-500 hover:bg-olive-600 text-white mb-6">
            Поддержка
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Часто задаваемые вопросы
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Найдите ответы на самые популярные вопросы о наших услугах и лечении
          </p>

          {/* Search and Filters */}
          <div className="max-w-2xl mx-auto space-y-4">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Поиск по вопросам и ответам..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 text-lg border-olive-200 focus:border-olive-400"
              />
            </div>

            {/* Category filters */}
            <div className="flex flex-wrap gap-2 justify-center">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                onClick={() => setSelectedCategory('all')}
                className={selectedCategory === 'all' ? 
                  'bg-olive-500 hover:bg-olive-600 text-white' : 
                  'border-olive-200 text-olive-700 hover:bg-olive-50'
                }
              >
                Все категории
              </Button>
              {categories.map(category => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  onClick={() => setSelectedCategory(category)}
                  className={selectedCategory === category ? 
                    'bg-olive-500 hover:bg-olive-600 text-white' : 
                    'border-olive-200 text-olive-700 hover:bg-olive-50'
                  }
                >
                  {category}
                </Button>
              ))}
            </div>

            {/* Expand/Collapse controls */}
            {filteredFAQs.length > 0 && (
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={expandAll}
                  className="border-olive-200 text-olive-700 hover:bg-olive-50"
                >
                  Развернуть все
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={collapseAll}
                  className="border-olive-200 text-olive-700 hover:bg-olive-50"
                >
                  Свернуть все
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto">
          {filteredFAQs.length > 0 ? (
            <div className="space-y-4">
              {filteredFAQs.map((faq, index) => (
                <FAQItem
                  key={faq.id}
                  faq={faq}
                  index={index}
                  isOpen={openItems.has(faq.id)}
                  onToggle={() => toggleItem(faq.id)}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-24 h-24 mx-auto mb-6 bg-olive-100 rounded-full flex items-center justify-center">
                  <HelpCircleIcon className="h-12 w-12 text-olive-500" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {searchTerm ? 'Вопросы не найдены' : 'Вопросов пока нет'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {searchTerm
                    ? 'Попробуйте изменить поисковый запрос или выбрать другую категорию'
                    : 'Мы работаем над наполнением этого раздела'
                  }
                </p>
                {searchTerm && (
                  <Button
                    variant="outline"
                    onClick={() => setSearchTerm('')}
                    className="border-olive-200 text-olive-700 hover:bg-olive-50"
                  >
                    Очистить поиск
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Contact CTA */}
        <div className="mt-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-olive-100 text-center max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Не нашли ответ на свой вопрос?
            </h3>
            <p className="text-gray-600 mb-6">
              Свяжитесь с нами, и наши специалисты ответят на все ваши вопросы
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-olive-500 hover:bg-olive-600 text-white">
                Задать вопрос
              </Button>
              <Button variant="outline" className="border-olive-200 text-olive-700 hover:bg-olive-50">
                Записаться на консультацию
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
