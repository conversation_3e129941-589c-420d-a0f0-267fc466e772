import * as React from 'react';
import { Edit } from 'lucide-react';

interface EditButtonProps {
  collection: string;
  id: string;
  position?: 'top-right' | 'bottom-right' | 'inline';
  size?: 'sm' | 'md';
  variant?: 'icon' | 'text';
  className?: string;
}

export const EditButton: React.FC<EditButtonProps> = ({
  collection,
  id,
  position = 'top-right',
  size = 'sm',
  variant = 'icon',
  className = '',
}) => {
  const [isVisible, setIsVisible] = React.useState(false);

  React.useEffect(() => {
    // Показываем кнопки редактирования только если есть токен админа
    const hasAdminToken = localStorage.getItem('pb_token') !== null;
    setIsVisible(hasAdminToken);
  }, []);

  if (!isVisible) {
    return null;
  }

  const baseClasses = 'inline-flex items-center gap-1 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded transition-colors duration-200';
  
  const sizeClasses = {
    sm: 'text-xs p-1',
    md: 'text-sm p-2'
  };

  const positionClasses = {
    'top-right': 'absolute top-2 right-2 z-10',
    'bottom-right': 'absolute bottom-2 right-2 z-10',
    'inline': 'relative'
  };

  const iconSize = size === 'sm' ? 12 : 16;

  const handleClick = () => {
    const url = `/admin/edit/${collection}/${id}`;
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <button
      onClick={handleClick}
      className={`${baseClasses} ${sizeClasses[size]} ${positionClasses[position]} ${className}`}
      title={`Редактировать ${collection} (ID: ${id})`}
    >
      <Edit size={iconSize} />
      {variant === 'text' && (
        <span className="whitespace-nowrap">
          {size === 'sm' ? 'Ред.' : 'Редактировать'}
        </span>
      )}
    </button>
  );
};
