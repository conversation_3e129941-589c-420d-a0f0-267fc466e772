import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, ArrowRightIcon } from 'lucide-react';
import { type News } from '@/lib/api';

interface NewsPageProps {
  news: News[];
  isAuthenticated?: boolean;
}

export const NewsPage = ({ news, isAuthenticated = false }: NewsPageProps) => {
  const [visibleNews, setVisibleNews] = useState(6);

  const loadMore = () => {
    setVisibleNews(prev => prev + 6);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getImageUrl = (newsItem: News) => {
    if (!newsItem.image) return '/placeholder-news.jpg';
    
    if (typeof newsItem.image === 'string') {
      return `https://pb.stom-line.ru/api/files/news/${newsItem.id}/${newsItem.image}`;
    }
    
    if (newsItem.image && typeof newsItem.image === 'object' && newsItem.image.filename) {
      return `https://pb.stom-line.ru/api/files/news/${newsItem.id}/${newsItem.image.filename}`;
    }
    
    return '/placeholder-news.jpg';
  };

  const getExcerpt = (content?: string) => {
    if (!content) return '';
    const textContent = content.replace(/<[^>]*>/g, '');
    return textContent.length > 150 ? textContent.substring(0, 150) + '...' : textContent;
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]" />

      <div className="relative z-10 container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Новости клиники
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Следите за последними новостями, достижениями и событиями нашей стоматологической клиники
          </p>
        </div>

        {/* Featured News */}
        {news.length > 0 && news[0].is_featured && (
          <div className="mb-16">
            <Card className="overflow-hidden border-0 shadow-2xl bg-white/80 backdrop-blur-sm">
              <div className="grid md:grid-cols-2 gap-0">
                <div className="relative h-64 md:h-full">
                  <img
                    src={getImageUrl(news[0])}
                    alt={news[0].title}
                    className="w-full h-full object-cover"
                  />
                  <Badge className="absolute top-4 left-4 bg-olive-500 hover:bg-olive-600">
                    Главная новость
                  </Badge>
                </div>
                <div className="p-8 flex flex-col justify-center">
                  <div className="flex items-center gap-2 text-sm text-gray-500 mb-4">
                    <CalendarIcon className="h-4 w-4" />
                    {formatDate(news[0].date)}
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                    {news[0].title}
                  </h2>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {getExcerpt(news[0].content)}
                  </p>
                  <Button 
                    asChild 
                    className="bg-olive-500 hover:bg-olive-600 text-white w-fit"
                  >
                    <a href={`/news/${news[0].slug}`}>
                      Читать полностью
                      <ArrowRightIcon className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* News Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {news.slice(news[0]?.is_featured ? 1 : 0, visibleNews).map((newsItem) => (
            <Card 
              key={newsItem.id} 
              className="overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:scale-105"
            >
              <div className="relative h-48">
                <img
                  src={getImageUrl(newsItem)}
                  alt={newsItem.title}
                  className="w-full h-full object-cover"
                />
                {newsItem.is_featured && (
                  <Badge className="absolute top-4 left-4 bg-olive-500 hover:bg-olive-600">
                    Рекомендуем
                  </Badge>
                )}
              </div>
              <CardHeader>
                <div className="flex items-center gap-2 text-sm text-gray-500 mb-2">
                  <CalendarIcon className="h-4 w-4" />
                  {formatDate(newsItem.date)}
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 line-clamp-2">
                  {newsItem.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600 mb-4 line-clamp-3">
                  {getExcerpt(newsItem.content)}
                </CardDescription>
                <Button 
                  asChild 
                  variant="outline" 
                  className="w-full border-olive-200 text-olive-700 hover:bg-olive-50"
                >
                  <a href={`/news/${newsItem.slug}`}>
                    Читать далее
                    <ArrowRightIcon className="ml-2 h-4 w-4" />
                  </a>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More Button */}
        {visibleNews < news.length && (
          <div className="text-center">
            <Button 
              onClick={loadMore}
              className="bg-olive-500 hover:bg-olive-600 text-white px-8 py-3"
            >
              Показать ещё новости
            </Button>
          </div>
        )}

        {/* Empty State */}
        {news.length === 0 && (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-olive-100 rounded-full flex items-center justify-center">
                <CalendarIcon className="h-12 w-12 text-olive-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Новостей пока нет
              </h3>
              <p className="text-gray-600">
                Мы работаем над наполнением этого раздела. Следите за обновлениями!
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
