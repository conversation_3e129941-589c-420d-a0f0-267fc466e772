import { useState } from 'react';
import { motion } from 'framer-motion';
import { StarIcon, UserIcon, CalendarIcon, SearchIcon, FilterIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { EditButton } from '@/components/admin/EditButton';
import { type Review } from '@/lib/api';

interface ReviewsPageProps {
  reviews: Review[];
}

interface ReviewCardProps {
  review: Review;
  index: number;
}

const ReviewCard = ({ review, index }: ReviewCardProps) => {
  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getImageUrl = (review: Review) => {
    if (!review.image) return null;
    return `https://pb.stom-line.ru/api/files/reviews/${review.id}/${review.image}`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
    >
      <Card className="h-full bg-white/80 backdrop-blur-sm border-olive-100 shadow-sm hover:shadow-md transition-all duration-300 group">
        <CardHeader className="relative">
          <EditButton
            collection="reviews"
            id={review.id}
            position="top-right"
            variant="text"
            size="sm"
            className="bg-white/90 border border-gray-200 shadow-sm"
          />
          
          <div className="flex items-start gap-4">
            {getImageUrl(review) ? (
              <img
                src={getImageUrl(review)!}
                alt={review.author}
                className="w-16 h-16 rounded-full object-cover border-2 border-olive-200"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-olive-100 flex items-center justify-center border-2 border-olive-200">
                <UserIcon className="h-8 w-8 text-olive-600" />
              </div>
            )}
            
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">
                {review.author}
              </h3>
              {review.date && (
                <div className="flex items-center gap-1 text-sm text-gray-500 mt-1">
                  <CalendarIcon className="h-4 w-4" />
                  {formatDate(review.date)}
                </div>
              )}
              
              {/* Rating stars */}
              <div className="flex items-center gap-1 mt-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <StarIcon
                    key={star}
                    className="h-4 w-4 fill-yellow-400 text-yellow-400"
                  />
                ))}
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {review.title && (
            <h4 className="text-lg font-medium text-gray-900 mb-3">
              {review.title}
            </h4>
          )}
          
          {review.content && (
            <div 
              className="prose prose-olive max-w-none text-gray-700 leading-relaxed"
              dangerouslySetInnerHTML={{ __html: review.content }}
            />
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const ReviewsPage = ({ reviews }: ReviewsPageProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [visibleReviews, setVisibleReviews] = useState(9);

  // Фильтрация отзывов
  const filteredReviews = reviews.filter(review => {
    const matchesSearch = review.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (review.title && review.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (review.content && review.content.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesSearch && review.is_published !== false;
  });

  const loadMore = () => {
    setVisibleReviews(prev => prev + 9);
  };

  return (
    <div className="relative min-h-screen bg-gradient-to-b from-olive-50 via-olive-100 to-olive-50">
      {/* Background elements */}
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -left-[10%] top-[20%] h-[300px] w-[300px] rounded-full bg-olive-300/30 blur-[100px]" />
        <div className="absolute -right-[5%] top-[40%] h-[200px] w-[200px] rounded-full bg-olive-400/30 blur-[80px]" />
        <div className="absolute bottom-[30%] left-[30%] h-[250px] w-[250px] rounded-full bg-olive-300/20 blur-[120px]" />
      </div>

      {/* Grid lines */}
      <div className="pointer-events-none absolute inset-0 z-0 bg-[url('/grid.svg')] bg-center opacity-[0.05]" />

      <div className="relative z-10 container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge className="bg-olive-500 hover:bg-olive-600 text-white mb-6">
            Отзывы пациентов
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Отзывы наших пациентов
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Узнайте, что говорят о нас наши пациенты. Их мнение — наша лучшая рекомендация
          </p>

          {/* Search */}
          <div className="max-w-md mx-auto">
            <div className="relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Поиск по отзывам..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12 text-lg border-olive-200 focus:border-olive-400"
              />
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          <div className="text-center bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-olive-100">
            <div className="text-3xl font-bold text-olive-600 mb-2">
              {filteredReviews.length}
            </div>
            <div className="text-gray-600">Отзывов</div>
          </div>
          
          <div className="text-center bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-olive-100">
            <div className="flex items-center justify-center gap-1 mb-2">
              <span className="text-3xl font-bold text-olive-600">5.0</span>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <StarIcon
                    key={star}
                    className="h-6 w-6 fill-yellow-400 text-yellow-400"
                  />
                ))}
              </div>
            </div>
            <div className="text-gray-600">Средняя оценка</div>
          </div>
          
          <div className="text-center bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-olive-100">
            <div className="text-3xl font-bold text-olive-600 mb-2">
              100%
            </div>
            <div className="text-gray-600">Рекомендуют нас</div>
          </div>
        </div>

        {/* Reviews Grid */}
        {filteredReviews.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {filteredReviews.slice(0, visibleReviews).map((review, index) => (
                <ReviewCard key={review.id} review={review} index={index} />
              ))}
            </div>

            {/* Load More Button */}
            {visibleReviews < filteredReviews.length && (
              <div className="text-center">
                <Button 
                  onClick={loadMore}
                  className="bg-olive-500 hover:bg-olive-600 text-white px-8 py-3"
                >
                  Показать ещё отзывы
                </Button>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-olive-100 rounded-full flex items-center justify-center">
                <UserIcon className="h-12 w-12 text-olive-500" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {searchTerm ? 'Отзывы не найдены' : 'Отзывов пока нет'}
              </h3>
              <p className="text-gray-600 mb-6">
                {searchTerm
                  ? 'Попробуйте изменить поисковый запрос'
                  : 'Станьте первым, кто оставит отзыв о нашей работе'
                }
              </p>
              {searchTerm && (
                <Button
                  variant="outline"
                  onClick={() => setSearchTerm('')}
                  className="border-olive-200 text-olive-700 hover:bg-olive-50"
                >
                  Очистить поиск
                </Button>
              )}
            </div>
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-olive-100 text-center max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Хотите поделиться своим опытом?
            </h3>
            <p className="text-gray-600 mb-6">
              Ваш отзыв поможет другим пациентам сделать правильный выбор
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button className="bg-olive-500 hover:bg-olive-600 text-white">
                Оставить отзыв
              </Button>
              <Button variant="outline" className="border-olive-200 text-olive-700 hover:bg-olive-50">
                Записаться на прием
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
